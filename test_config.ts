import dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * Test script to verify configuration settings
 */
function testConfiguration() {
  console.log("🔧 Testing Bot Configuration...\n");

  // Check network configuration first
  const useTestnet = process.env.USE_TESTNET === "true";

  // Check API credentials based on network
  const testnetApiKey = process.env.TESTNET_API_KEY;
  const testnetApiSecret = process.env.TESTNET_API_SECRET;
  const mainnetApiKey = process.env.MAINNET_API_KEY;
  const mainnetApiSecret = process.env.MAINNET_API_SECRET;

  console.log("📋 API Configuration:");
  console.log("  Testnet:");
  console.log(
    `    API Key: ${
      testnetApiKey ? `${testnetApiKey.substring(0, 8)}...` : "❌ Not set"
    }`
  );
  console.log(
    `    API Secret: ${
      testnetApiSecret ? `${testnetApiSecret.substring(0, 8)}...` : "❌ Not set"
    }`
  );
  console.log("  Mainnet:");
  console.log(
    `    API Key: ${
      mainnetApiKey ? `${mainnetApiKey.substring(0, 8)}...` : "❌ Not set"
    }`
  );
  console.log(
    `    API Secret: ${
      mainnetApiSecret ? `${mainnetApiSecret.substring(0, 8)}...` : "❌ Not set"
    }`
  );

  // Show which keys will be used
  const activeApiKey = useTestnet ? testnetApiKey : mainnetApiKey;
  const activeApiSecret = useTestnet ? testnetApiSecret : mainnetApiSecret;
  console.log("  Currently Active:");
  console.log(
    `    API Key: ${
      activeApiKey ? `${activeApiKey.substring(0, 8)}...` : "❌ Not set"
    }`
  );
  console.log(
    `    API Secret: ${
      activeApiSecret ? `${activeApiSecret.substring(0, 8)}...` : "❌ Not set"
    }`
  );
  console.log("\n🌐 Network Configuration:");
  console.log(
    `  Network: ${useTestnet ? "🧪 TESTNET (Safe)" : "🔴 MAINNET (Real money)"}`
  );

  if (useTestnet) {
    console.log(`  HTTP Futures: ${process.env.TESTNET_HTTP_FUTURES}`);
    console.log(`  WS Futures: ${process.env.TESTNET_WS_FUTURES}`);
  } else {
    console.log(`  HTTP Futures: ${process.env.MAINNET_HTTP_FUTURES}`);
    console.log(`  WS Futures: ${process.env.MAINNET_WS_FUTURES}`);
  }

  // Check trading configuration
  const enableRealTrading = process.env.ENABLE_REAL_TRADING === "true";
  console.log("\n💰 Trading Configuration:");
  console.log(`  Symbol: ${process.env.TRADING_SYMBOL}`);
  console.log(`  Initial Balance: ${process.env.INITIAL_BALANCE} USDT`);
  console.log(`  Max Position Size: ${process.env.MAX_POSITION_SIZE}`);
  console.log(
    `  Real Trading: ${
      enableRealTrading ? "✅ ENABLED" : "📝 DISABLED (Paper trading)"
    }`
  );

  // Check risk management
  console.log("\n🛡️ Risk Management:");
  console.log(`  Max Daily Loss: ${process.env.MAX_DAILY_LOSS} USDT`);
  console.log(`  Emergency Stop Loss: ${process.env.EMERGENCY_STOP_LOSS}%`);

  // Check web dashboard
  console.log("\n🌐 Web Dashboard:");
  console.log(`  Port: ${process.env.WEB_PORT}`);
  console.log(`  URL: http://localhost:${process.env.WEB_PORT}`);

  // Safety warnings
  console.log("\n⚠️ Safety Status:");

  if (useTestnet) {
    console.log("  ✅ Using TESTNET - Safe for testing");
  } else {
    console.log("  🔴 Using MAINNET - Real money at risk!");
  }

  if (!enableRealTrading) {
    console.log("  ✅ Paper trading mode - No real trades");
  } else {
    console.log("  ⚠️ Real trading enabled - Trades will be placed!");
  }

  // Configuration recommendations
  console.log("\n💡 Recommendations:");

  if (useTestnet && !enableRealTrading) {
    console.log("  🎯 Perfect for initial testing - safest configuration");
  } else if (useTestnet && enableRealTrading) {
    console.log("  🧪 Good for testing trading logic with fake money");
  } else if (!useTestnet && !enableRealTrading) {
    console.log("  📊 Good for testing with real market data, no trades");
  } else {
    console.log("  🚨 LIVE TRADING MODE - Use with extreme caution!");
    console.log("  🔍 Make sure you have tested thoroughly first");
  }

  console.log("\n✅ Configuration test complete!");
}

// Run the test
testConfiguration();
