# 🚀 Ultimate 8X Real-Time Trading Bot

A sophisticated real-time cryptocurrency trading bot that implements the Ultimate 8X Strategy with live Binance integration, comprehensive logging, and a beautiful web dashboard.

## ✨ Features

- **Real-time Trading**: Live integration with Binance API for real-time market data and order execution
- **Ultimate 8X Strategy**: Implements the proven strategy that achieved 8x returns in backtesting
- **Adaptive Market Regimes**: Automatically adapts parameters based on market conditions (EXPLOSIVE, TRENDING, CHOPPY, DEAD)
- **Comprehensive Logging**: Console, file, and web dashboard logging for all trades and activities
- **Web Dashboard**: Beautiful real-time dashboard to monitor trades, balance, and performance
- **Risk Management**: Built-in stop losses, position sizing, and daily loss limits
- **Paper Trading**: Test the bot without real money before going live

## 🛠️ Setup Instructions

### 1. Configure Environment Variables

Edit the `.env` file with your settings:

```env
# Binance API Configuration
BINANCE_API_KEY=your_actual_binance_api_key
BINANCE_API_SECRET=your_actual_binance_api_secret

# Network Configuration (testnet vs mainnet)
USE_TESTNET=true  # Start with testnet for safe testing
# Testnet URLs
TESTNET_HTTP_FUTURES=https://testnet.binancefuture.com
TESTNET_WS_FUTURES=wss://testnet.binancefuture.com/ws-fapi/v1
# Mainnet URLs (default Binance URLs)
MAINNET_HTTP_FUTURES=https://fapi.binance.com
MAINNET_WS_FUTURES=wss://fstream.binance.com/ws

# Trading Configuration
TRADING_SYMBOL=BTCUSDT
INITIAL_BALANCE=100
MAX_POSITION_SIZE=0.001
ENABLE_REAL_TRADING=false  # Set to true for live trading

# Web Dashboard Configuration
WEB_PORT=3000

# Risk Management
MAX_DAILY_LOSS=50
EMERGENCY_STOP_LOSS=80
```

### 2. Get Binance API Keys

#### For Testnet (Recommended First):

1. Go to [Binance Testnet](https://testnet.binancefuture.com/)
2. Login with your Binance account
3. Go to API Management and create a new API key
4. Copy the API Key and Secret Key to your `.env` file
5. Keep `USE_TESTNET=true` in your `.env` file

#### For Mainnet (Real Trading):

1. Go to [Binance API Management](https://www.binance.com/en/my/settings/api-management)
2. Create a new API key
3. Enable "Enable Spot & Margin Trading" if you want to trade
4. Add your IP address to the whitelist for security
5. Copy the API Key and Secret Key to your `.env` file
6. Set `USE_TESTNET=false` in your `.env` file

### 3. Install Dependencies

```bash
npm install
```

### 4. Start the Bot

#### Step 1: Testnet Paper Trading (Safest)

```bash
# Make sure USE_TESTNET=true and ENABLE_REAL_TRADING=false
npm run bot
```

#### Step 2: Testnet Live Trading (Safe with fake money)

1. Set `ENABLE_REAL_TRADING=true` in `.env`
2. Keep `USE_TESTNET=true`
3. Run: `npm run bot`

#### Step 3: Mainnet Paper Trading (Real data, no trades)

1. Set `USE_TESTNET=false` in `.env`
2. Set `ENABLE_REAL_TRADING=false`
3. Run: `npm run bot`

#### Step 4: Mainnet Live Trading (Real money - be careful!)

1. Set `USE_TESTNET=false` in `.env`
2. Set `ENABLE_REAL_TRADING=true`
3. Run: `npm run bot`

## 📊 Web Dashboard

Once the bot is running, access the web dashboard at:

```
http://localhost:3000
```

The dashboard shows:

- Real-time balance and PnL
- Active positions
- Trade history with detailed logs
- Market state and indicators
- Win rate and performance metrics

## 🔧 Configuration Options

### Trading Parameters

- `TRADING_SYMBOL`: The cryptocurrency pair to trade (default: BTCUSDT)
- `INITIAL_BALANCE`: Starting balance for calculations (default: 100 USDT)
- `MAX_POSITION_SIZE`: Maximum position size in base currency (default: 0.001 BTC)
- `ENABLE_REAL_TRADING`: Enable live trading vs paper trading (default: false)

### Risk Management

- `MAX_DAILY_LOSS`: Maximum daily loss before stopping (default: 50 USDT)
- `EMERGENCY_STOP_LOSS`: Emergency stop if balance drops below this % (default: 80%)

### Strategy Parameters

The bot automatically adapts these based on market regime:

- RSI oversold/overbought levels
- Leverage multiplier
- Risk per trade
- Stop loss percentage
- Take profit percentage

## 📈 Strategy Overview

The Ultimate 8X Strategy uses:

1. **Technical Indicators**: RSI, EMA (8/21), SMA (50)
2. **Market Regime Detection**: Volatility, momentum, and volume analysis
3. **Adaptive Parameters**: Strategy adjusts based on market conditions
4. **Risk Management**: Tight stops with high reward ratios

### Market Regimes

- **EXPLOSIVE**: High volatility, high volume, aggressive parameters
- **TRENDING**: Strong directional movement, moderate parameters
- **CHOPPY**: Sideways movement, conservative parameters
- **DEAD**: Low activity, minimal trading

## 🚨 Safety Features

- **Paper Trading Mode**: Test without real money
- **Daily Loss Limits**: Automatic shutdown if daily losses exceed limit
- **Emergency Stops**: Hard stops if balance drops too low
- **Position Size Limits**: Maximum position size caps
- **Error Handling**: Comprehensive error handling and logging

## 📝 Logging

The bot creates detailed logs in multiple formats:

1. **Console Logs**: Real-time colored output
2. **File Logs**: Daily log files in `./logs/` directory
3. **Web Logs**: JSON format for the web dashboard

## 🔍 Monitoring

Monitor your bot through:

1. **Console Output**: Real-time status updates
2. **Web Dashboard**: Visual interface with charts and stats
3. **Log Files**: Detailed trade history and analysis

## ⚠️ Important Warnings

1. **Start with Paper Trading**: Always test with `ENABLE_REAL_TRADING=false` first
2. **Use Small Amounts**: Start with small position sizes when going live
3. **Monitor Closely**: Keep an eye on the bot, especially initially
4. **API Security**: Keep your API keys secure and use IP whitelisting
5. **Market Risk**: Cryptocurrency trading involves significant risk

## 🛑 Stopping the Bot

To stop the bot safely:

- Press `Ctrl+C` in the terminal
- The bot will close any open positions and shut down gracefully

## 📞 Support

If you encounter issues:

1. Check the console logs for error messages
2. Verify your API keys and permissions
3. Ensure sufficient balance for trading
4. Check network connectivity

## 🎯 Performance Expectations

Based on backtesting, the Ultimate 8X Strategy has shown:

- Target: 8x returns
- Success across multiple market conditions
- Adaptive performance based on market regime

**Remember**: Past performance does not guarantee future results. Always trade responsibly!
