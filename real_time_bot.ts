import Binance from "binance-api-node";
import dotenv from "dotenv";
import { setIntervalAsync } from "set-interval-async/dynamic";
import { EMA, RSI, SMA } from "technicalindicators";
import { TradeLogger } from "./trade_logger.js";
import { WebServer } from "./web_server.js";

const esmBinanceHack: typeof Binance = (Binance as any).default;
// Load environment variables
dotenv.config();

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  highestPrice?: number;
  lowestPrice?: number;
  entryReason: string;
  orderId?: string;
}

interface MarketState {
  volatility: number;
  momentum: number;
  volume: number;
  trend: number;
  regime: "EXPLOSIVE" | "TRENDING" | "CHOPPY" | "DEAD";
}

interface Trade {
  entryTime: string;
  exitTime: string;
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  size: number;
  pnl: number;
  pnlPercent: number;
  leverage: number;
  entryReason: string;
  exitReason: string;
  orderId?: string;
}

export class RealTimeTradingBot {
  private binance: any;
  private logger: TradeLogger;
  private webServer: WebServer;
  private symbol: string;
  private balance: number;
  private initialBalance: number;
  private position: Position;
  private trades: Trade[] = [];
  private candleData: CandleData[] = [];
  private maxCandles = 200;
  private isRunning = false;
  private marketState: MarketState;
  private enableRealTrading: boolean;
  private maxPositionSize: number;
  private maxDailyLoss: number;
  private emergencyStopLoss: number;
  private dailyPnL = 0;
  private lastDayReset = new Date().getDate();

  // Strategy parameters (from Ultimate 8X Strategy)
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 8;
  private readonly EMA_SLOW = 21;
  private readonly SMA_PERIOD = 50;
  private readonly VOLATILITY_PERIOD = 20;
  private RSI_OVERSOLD = 10;
  private RSI_OVERBOUGHT = 90;
  private LEVERAGE = 75;
  private RISK_PER_TRADE = 0.5;
  private STOP_LOSS_PERCENT = 0.005;
  private TAKE_PROFIT_PERCENT = 0.4;
  private VOLUME_MULTIPLIER = 2.0;
  private MOMENTUM_THRESHOLD = 0.01;

  constructor() {
    // Initialize configuration
    this.symbol = process.env.TRADING_SYMBOL || "BTCUSDT";
    this.initialBalance = parseFloat(process.env.INITIAL_BALANCE || "100");
    this.balance = this.initialBalance;
    this.enableRealTrading = process.env.ENABLE_REAL_TRADING === "true";
    this.maxPositionSize = parseFloat(process.env.MAX_POSITION_SIZE || "0.001");
    this.maxDailyLoss = parseFloat(process.env.MAX_DAILY_LOSS || "50");
    this.emergencyStopLoss = parseFloat(
      process.env.EMERGENCY_STOP_LOSS || "80"
    );

    // Initialize position
    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };

    // Initialize market state
    this.marketState = {
      volatility: 0,
      momentum: 0,
      volume: 0,
      trend: 0,
      regime: "CHOPPY",
    };

    // Initialize logger and web server
    this.logger = new TradeLogger();
    this.webServer = new WebServer(
      parseInt(process.env.WEB_PORT || "3000"),
      this.logger.getWebLogsPath()
    );

    // Initialize Binance client
    this.initializeBinance();
  }

  private initializeBinance(): void {
    const useTestnet = process.env.USE_TESTNET === "true";

    // Select API keys based on network
    const apiKey = useTestnet
      ? process.env.TESTNET_API_KEY
      : process.env.MAINNET_API_KEY;
    const apiSecret = useTestnet
      ? process.env.TESTNET_API_SECRET
      : process.env.MAINNET_API_SECRET;

    if (!apiKey || !apiSecret) {
      const networkType = useTestnet ? "testnet" : "mainnet";
      this.logger.error(
        `${networkType.toUpperCase()} API credentials not found in environment variables`
      );
      this.logger.error(
        `Please set ${networkType.toUpperCase()}_API_KEY and ${networkType.toUpperCase()}_API_SECRET in .env file`
      );
      throw new Error(`Missing ${networkType} Binance API credentials`);
    }

    // Check for default placeholder values
    const defaultKeys = [
      "your_testnet_api_key_here",
      "your_mainnet_api_key_here",
      "your_binance_api_key_here",
    ];
    const defaultSecrets = [
      "your_testnet_api_secret_here",
      "your_mainnet_api_secret_here",
      "your_binance_api_secret_here",
    ];

    if (defaultKeys.includes(apiKey) || defaultSecrets.includes(apiSecret)) {
      const networkType = useTestnet ? "testnet" : "mainnet";
      this.logger.error(
        `Please update your ${networkType} Binance API credentials in the .env file`
      );
      throw new Error(
        `Default ${networkType} API credentials detected - please update .env file`
      );
    }

    // Configure URLs based on testnet/mainnet setting
    const binanceConfig: any = {
      apiKey,
      apiSecret,
      getTime: () => Date.now(),
    };

    if (useTestnet) {
      binanceConfig.httpFutures = process.env.TESTNET_HTTP_FUTURES;
      binanceConfig.wsFutures = process.env.TESTNET_WS_FUTURES;
      this.logger.info("🧪 Using Binance TESTNET for safe testing");
      this.logger.warning("TESTNET MODE: No real money will be used");
    } else {
      binanceConfig.httpFutures = process.env.MAINNET_HTTP_FUTURES;
      binanceConfig.wsFutures = process.env.MAINNET_WS_FUTURES;
      this.logger.info("🔴 Using Binance MAINNET - REAL TRADING ENVIRONMENT");
      this.logger.warning(
        "MAINNET MODE: Real money will be used if trading is enabled"
      );
    }

    this.binance = esmBinanceHack(binanceConfig);

    this.logger.info("✅ Binance client initialized successfully");
    this.logger.info(`🌐 Network: ${useTestnet ? "TESTNET" : "MAINNET"}`);
    this.logger.info(`🔗 HTTP Futures: ${binanceConfig.httpFutures}`);
    this.logger.info(`🔗 WS Futures: ${binanceConfig.wsFutures}`);
  }

  async start(): Promise<void> {
    try {
      this.logger.info("🚀 Starting Ultimate 8X Real-Time Trading Bot...");

      // Start web server
      await this.webServer.start();

      // Validate account and permissions
      await this.validateAccount();

      // Load initial historical data
      await this.loadInitialData();

      // Start real-time data stream
      this.startRealTimeStream();

      // Start trading loop
      this.startTradingLoop();

      this.isRunning = true;
      this.logger.info(`✅ Bot started successfully! Trading ${this.symbol}`);
      this.logger.info(`💰 Initial balance: ${this.balance} USDT`);
      this.logger.info(
        `🎯 Target: ${this.initialBalance * 8} USDT (8x return)`
      );
      this.logger.info(
        `⚡ Real trading: ${
          this.enableRealTrading ? "ENABLED" : "DISABLED (Paper trading)"
        }`
      );
      this.logger.info(
        `🌐 Dashboard: http://localhost:${this.webServer.getPort()}`
      );
    } catch (error) {
      this.logger.error("Failed to start bot", error);
      throw error;
    }
  }

  private async validateAccount(): Promise<void> {
    try {
      const accountInfo = await this.binance.accountInfo();
      this.logger.info(`Account validated. Can trade: ${accountInfo.canTrade}`);

      if (!accountInfo.canTrade) {
        throw new Error("Account does not have trading permissions");
      }

      // Check USDT balance
      const usdtBalance = accountInfo.balances.find(
        (b: any) => b.asset === "USDT"
      );
      if (usdtBalance) {
        const availableBalance = parseFloat(usdtBalance.free);
        this.logger.info(`Available USDT balance: ${availableBalance}`);

        if (availableBalance < this.initialBalance) {
          this.logger.warning(
            `Available balance (${availableBalance}) is less than initial balance (${this.initialBalance})`
          );
        }
      }
    } catch (error) {
      this.logger.error("Account validation failed", error);
      throw error;
    }
  }

  private async loadInitialData(): Promise<void> {
    try {
      this.logger.info("Loading initial historical data...");

      const klines = await this.binance.candles({
        symbol: this.symbol,
        interval: "15m",
        limit: this.maxCandles,
      });

      this.candleData = klines.map((kline: any) => ({
        time: new Date(kline.openTime).toISOString(),
        open: parseFloat(kline.open),
        high: parseFloat(kline.high),
        low: parseFloat(kline.low),
        close: parseFloat(kline.close),
        volume: parseFloat(kline.volume),
      }));

      this.logger.info(`Loaded ${this.candleData.length} historical candles`);
    } catch (error) {
      this.logger.error("Failed to load initial data", error);
      throw error;
    }
  }

  private startRealTimeStream(): void {
    this.logger.info("Starting real-time data stream...");

    const stream = this.binance.ws.candles(
      this.symbol,
      "15m",
      (candle: any) => {
        if (candle.isFinal) {
          const newCandle: CandleData = {
            time: new Date(candle.startTime).toISOString(),
            open: parseFloat(candle.open),
            high: parseFloat(candle.high),
            low: parseFloat(candle.low),
            close: parseFloat(candle.close),
            volume: parseFloat(candle.volume),
          };

          // Add new candle and maintain max length
          this.candleData.push(newCandle);
          if (this.candleData.length > this.maxCandles) {
            this.candleData.shift();
          }

          this.logger.info(
            `New candle: ${this.symbol} @ ${
              newCandle.close
            } | Vol: ${newCandle.volume.toFixed(2)}`
          );
        }
      }
    );

    // Handle stream errors
    stream.on("error", (error: any) => {
      this.logger.error("WebSocket stream error", error);
    });

    stream.on("close", () => {
      this.logger.warning("WebSocket stream closed");
    });
  }

  private startTradingLoop(): void {
    this.logger.info("Starting trading analysis loop...");

    // Run analysis every 30 seconds
    setIntervalAsync(async () => {
      if (
        !this.isRunning ||
        this.candleData.length < this.SMA_PERIOD + this.VOLATILITY_PERIOD
      ) {
        return;
      }

      try {
        await this.runTradingAnalysis();
      } catch (error) {
        this.logger.error("Trading analysis error", error);
      }
    }, 30000);
  }

  private async runTradingAnalysis(): Promise<void> {
    // Reset daily PnL if new day
    const currentDay = new Date().getDate();
    if (currentDay !== this.lastDayReset) {
      this.dailyPnL = 0;
      this.lastDayReset = currentDay;
      this.logger.info("Daily PnL reset for new day");
    }

    // Check emergency stops
    if (this.dailyPnL <= -this.maxDailyLoss) {
      this.logger.warning(
        `Daily loss limit reached: ${this.dailyPnL.toFixed(2)} USDT`
      );
      if (this.position.side) {
        await this.closePosition("DAILY_LOSS_LIMIT");
      }
      return;
    }

    if (this.balance <= this.initialBalance * (this.emergencyStopLoss / 100)) {
      this.logger.error(
        `Emergency stop loss triggered! Balance: ${this.balance.toFixed(
          2
        )} USDT`
      );
      if (this.position.side) {
        await this.closePosition("EMERGENCY_STOP");
      }
      return;
    }

    const currentCandle = this.candleData[this.candleData.length - 1];
    const indicators = this.calculateIndicators();

    if (!indicators) return;

    // Calculate market state
    this.marketState = this.calculateMarketState();
    this.adaptToMarketState(this.marketState);

    // Check for exit signals if position exists
    if (this.position.side) {
      const exitSignal = this.shouldExitPosition(currentCandle, indicators);
      if (exitSignal.exit) {
        await this.closePosition(exitSignal.reason);
      } else {
        // Update trailing stops
        this.updateTrailingStops(currentCandle.close);
      }
    }

    // Check for entry signals if no position
    if (!this.position.side) {
      const longSignal = this.shouldEnterLong(currentCandle, indicators);
      const shortSignal = this.shouldEnterShort(currentCandle, indicators);

      if (longSignal.enter) {
        await this.openPosition("LONG", currentCandle.close, longSignal.reason);
      } else if (shortSignal.enter) {
        await this.openPosition(
          "SHORT",
          currentCandle.close,
          shortSignal.reason
        );
      }
    }

    // Log current status
    this.logCurrentStatus(currentCandle, indicators);
  }

  private calculateIndicators(): any {
    if (
      this.candleData.length <
      Math.max(this.RSI_PERIOD, this.EMA_SLOW, this.SMA_PERIOD)
    ) {
      return null;
    }

    const closes = this.candleData.map((c) => c.close);
    const highs = this.candleData.map((c) => c.high);
    const lows = this.candleData.map((c) => c.low);
    const volumes = this.candleData.map((c) => c.volume);

    try {
      const rsiValues = RSI.calculate({
        values: closes,
        period: this.RSI_PERIOD,
      });
      const emaFastValues = EMA.calculate({
        values: closes,
        period: this.EMA_FAST,
      });
      const emaSlowValues = EMA.calculate({
        values: closes,
        period: this.EMA_SLOW,
      });
      const smaValues = SMA.calculate({
        values: closes,
        period: this.SMA_PERIOD,
      });

      return {
        rsi: rsiValues[rsiValues.length - 1] || 0,
        emaFast: emaFastValues[emaFastValues.length - 1] || 0,
        emaSlow: emaSlowValues[emaSlowValues.length - 1] || 0,
        sma: smaValues[smaValues.length - 1] || 0,
        prevRsi: rsiValues[rsiValues.length - 2] || 0,
        prevEmaFast: emaFastValues[emaFastValues.length - 2] || 0,
        prevEmaSlow: emaSlowValues[emaSlowValues.length - 2] || 0,
      };
    } catch (error) {
      this.logger.error("Error calculating indicators", error);
      return null;
    }
  }

  private calculateMarketState(): MarketState {
    if (this.candleData.length < this.VOLATILITY_PERIOD + this.SMA_PERIOD) {
      return this.marketState;
    }

    const recentCandles = this.candleData.slice(-this.VOLATILITY_PERIOD);
    const closes = recentCandles.map((c) => c.close);
    const volumes = recentCandles.map((c) => c.volume);
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);

    // Calculate volatility (ATR-based)
    let atrSum = 0;
    for (let i = 1; i < recentCandles.length; i++) {
      const tr = Math.max(
        highs[i] - lows[i],
        Math.abs(highs[i] - closes[i - 1]),
        Math.abs(lows[i] - closes[i - 1])
      );
      atrSum += tr;
    }
    const volatility =
      atrSum / (recentCandles.length - 1) / closes[closes.length - 1];

    // Calculate momentum
    const momentum = Math.abs(
      (closes[closes.length - 1] - closes[0]) / closes[0]
    );

    // Calculate volume ratio
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
    const currentVolume = volumes[volumes.length - 1];
    const volumeRatio = currentVolume / avgVolume;

    // Calculate trend
    const smaValues = SMA.calculate({
      values: closes,
      period: Math.min(10, closes.length),
    });
    const currentSMA = smaValues[smaValues.length - 1];
    const pastSMA = smaValues[Math.max(0, smaValues.length - 5)];
    const trend = Math.abs((currentSMA - pastSMA) / pastSMA);

    // Determine market regime
    let regime: "EXPLOSIVE" | "TRENDING" | "CHOPPY" | "DEAD";

    if (volatility > 0.03 && volumeRatio > 2.0 && momentum > 0.02) {
      regime = "EXPLOSIVE";
    } else if (volatility > 0.015 && volumeRatio > 1.3 && trend > 0.005) {
      regime = "TRENDING";
    } else if (volatility > 0.008 && volumeRatio > 0.8) {
      regime = "CHOPPY";
    } else {
      regime = "DEAD";
    }

    return {
      volatility,
      momentum,
      volume: volumeRatio,
      trend,
      regime,
    };
  }

  private adaptToMarketState(state: MarketState): void {
    switch (state.regime) {
      case "EXPLOSIVE":
        this.RSI_OVERSOLD = 5;
        this.RSI_OVERBOUGHT = 95;
        this.LEVERAGE = 100;
        this.RISK_PER_TRADE = 0.8;
        this.STOP_LOSS_PERCENT = 0.003;
        this.TAKE_PROFIT_PERCENT = 0.6;
        this.VOLUME_MULTIPLIER = 3.0;
        this.MOMENTUM_THRESHOLD = 0.025;
        break;

      case "TRENDING":
        this.RSI_OVERSOLD = 8;
        this.RSI_OVERBOUGHT = 92;
        this.LEVERAGE = 75;
        this.RISK_PER_TRADE = 0.6;
        this.STOP_LOSS_PERCENT = 0.004;
        this.TAKE_PROFIT_PERCENT = 0.5;
        this.VOLUME_MULTIPLIER = 2.5;
        this.MOMENTUM_THRESHOLD = 0.015;
        break;

      case "CHOPPY":
        this.RSI_OVERSOLD = 15;
        this.RSI_OVERBOUGHT = 85;
        this.LEVERAGE = 50;
        this.RISK_PER_TRADE = 0.3;
        this.STOP_LOSS_PERCENT = 0.006;
        this.TAKE_PROFIT_PERCENT = 0.25;
        this.VOLUME_MULTIPLIER = 1.8;
        this.MOMENTUM_THRESHOLD = 0.008;
        break;

      case "DEAD":
        this.RSI_OVERSOLD = 25;
        this.RSI_OVERBOUGHT = 75;
        this.LEVERAGE = 25;
        this.RISK_PER_TRADE = 0.15;
        this.STOP_LOSS_PERCENT = 0.01;
        this.TAKE_PROFIT_PERCENT = 0.15;
        this.VOLUME_MULTIPLIER = 1.2;
        this.MOMENTUM_THRESHOLD = 0.005;
        break;
    }
  }

  private shouldEnterLong(
    candle: CandleData,
    indicators: any
  ): { enter: boolean; reason: string } {
    const extremeOversold = indicators.rsi < this.RSI_OVERSOLD;
    const rsiRecovering =
      indicators.rsi > this.RSI_OVERSOLD &&
      indicators.rsi < this.RSI_OVERSOLD + 20;
    const emaGoldenCross =
      indicators.emaFast > indicators.emaSlow &&
      indicators.prevEmaFast <= indicators.prevEmaSlow;
    const priceAboveEMA = candle.close > indicators.emaFast;
    const strongMomentum = this.marketState.momentum > this.MOMENTUM_THRESHOLD;
    const ultraVolume = this.marketState.volume > this.VOLUME_MULTIPLIER;

    // Get previous candle for pattern analysis
    const prevCandle = this.candleData[this.candleData.length - 2];
    if (!prevCandle) return { enter: false, reason: "" };

    const engulfingPattern =
      candle.close > prevCandle.high && candle.open < prevCandle.close;
    const explosiveGreenCandle =
      (candle.close - candle.open) / candle.open > 0.02;
    const breakoutAboveSMA =
      candle.close > indicators.sma && prevCandle.close <= indicators.sma;

    switch (this.marketState.regime) {
      case "EXPLOSIVE":
        if (extremeOversold && explosiveGreenCandle && ultraVolume) {
          return { enter: true, reason: "EXTREME_OVERSOLD_EXPLOSIVE" };
        }
        if (emaGoldenCross && strongMomentum && ultraVolume) {
          return { enter: true, reason: "EMA_GOLDEN_CROSS_EXPLOSIVE" };
        }
        break;

      case "TRENDING":
        if (
          breakoutAboveSMA &&
          strongMomentum &&
          ultraVolume &&
          indicators.rsi < 40
        ) {
          return { enter: true, reason: "SMA_BREAKOUT_TREND" };
        }
        if (rsiRecovering && engulfingPattern && ultraVolume) {
          return { enter: true, reason: "RSI_RECOVERY_ENGULFING" };
        }
        break;

      case "CHOPPY":
        if (extremeOversold && engulfingPattern && ultraVolume) {
          return { enter: true, reason: "OVERSOLD_ENGULFING_CHOPPY" };
        }
        if (emaGoldenCross && priceAboveEMA && strongMomentum) {
          return { enter: true, reason: "EMA_CROSS_MOMENTUM_CHOPPY" };
        }
        break;

      case "DEAD":
        if (
          extremeOversold &&
          explosiveGreenCandle &&
          this.marketState.volume > 1.5
        ) {
          return { enter: true, reason: "OVERSOLD_VOLUME_DEAD" };
        }
        break;
    }

    return { enter: false, reason: "" };
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any
  ): { enter: boolean; reason: string } {
    const extremeOverbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const rsiDeclining =
      indicators.rsi < this.RSI_OVERBOUGHT &&
      indicators.rsi > this.RSI_OVERBOUGHT - 20;
    const emaDeathCross =
      indicators.emaFast < indicators.emaSlow &&
      indicators.prevEmaFast >= indicators.prevEmaSlow;
    const priceBelowEMA = candle.close < indicators.emaFast;
    const strongMomentum = this.marketState.momentum > this.MOMENTUM_THRESHOLD;
    const ultraVolume = this.marketState.volume > this.VOLUME_MULTIPLIER;

    const prevCandle = this.candleData[this.candleData.length - 2];
    if (!prevCandle) return { enter: false, reason: "" };

    const bearishEngulfing =
      candle.close < prevCandle.low && candle.open > prevCandle.close;
    const explosiveRedCandle =
      (candle.open - candle.close) / candle.open > 0.02;
    const breakdownBelowSMA =
      candle.close < indicators.sma && prevCandle.close >= indicators.sma;

    switch (this.marketState.regime) {
      case "EXPLOSIVE":
        if (extremeOverbought && explosiveRedCandle && ultraVolume) {
          return { enter: true, reason: "EXTREME_OVERBOUGHT_EXPLOSIVE" };
        }
        if (emaDeathCross && strongMomentum && ultraVolume) {
          return { enter: true, reason: "EMA_DEATH_CROSS_EXPLOSIVE" };
        }
        break;

      case "TRENDING":
        if (
          breakdownBelowSMA &&
          strongMomentum &&
          ultraVolume &&
          indicators.rsi > 60
        ) {
          return { enter: true, reason: "SMA_BREAKDOWN_TREND" };
        }
        if (rsiDeclining && bearishEngulfing && ultraVolume) {
          return { enter: true, reason: "RSI_DECLINE_ENGULFING" };
        }
        break;

      case "CHOPPY":
        if (extremeOverbought && bearishEngulfing && ultraVolume) {
          return { enter: true, reason: "OVERBOUGHT_ENGULFING_CHOPPY" };
        }
        if (emaDeathCross && priceBelowEMA && strongMomentum) {
          return { enter: true, reason: "EMA_CROSS_MOMENTUM_CHOPPY" };
        }
        break;

      case "DEAD":
        if (
          extremeOverbought &&
          explosiveRedCandle &&
          this.marketState.volume > 1.5
        ) {
          return { enter: true, reason: "OVERBOUGHT_VOLUME_DEAD" };
        }
        break;
    }

    return { enter: false, reason: "" };
  }

  private shouldExitPosition(
    candle: CandleData,
    indicators: any
  ): { exit: boolean; reason: string } {
    if (!this.position.side) return { exit: false, reason: "" };

    const currentPrice = candle.close;
    const pnlPercent =
      this.position.side === "LONG"
        ? (currentPrice - this.position.entryPrice) / this.position.entryPrice
        : (this.position.entryPrice - currentPrice) / this.position.entryPrice;

    // Hard stop loss - NEVER exceed this
    if (pnlPercent <= -this.STOP_LOSS_PERCENT) {
      return { exit: true, reason: "STOP_LOSS" };
    }

    // Take profit at target
    if (pnlPercent >= this.TAKE_PROFIT_PERCENT) {
      return { exit: true, reason: "TAKE_PROFIT" };
    }

    // Regime-specific exits
    let rsiExitLong = 85;
    let rsiExitShort = 15;
    let trailingActivation = 0.02;

    switch (this.marketState.regime) {
      case "EXPLOSIVE":
        rsiExitLong = 95;
        rsiExitShort = 5;
        trailingActivation = 0.05;
        break;
      case "TRENDING":
        rsiExitLong = 90;
        rsiExitShort = 10;
        trailingActivation = 0.03;
        break;
      case "CHOPPY":
        rsiExitLong = 80;
        rsiExitShort = 20;
        trailingActivation = 0.015;
        break;
      case "DEAD":
        rsiExitLong = 70;
        rsiExitShort = 30;
        trailingActivation = 0.01;
        break;
    }

    // RSI reversal signals
    if (this.position.side === "LONG" && indicators.rsi > rsiExitLong) {
      return { exit: true, reason: "RSI_OVERBOUGHT_EXIT" };
    }
    if (this.position.side === "SHORT" && indicators.rsi < rsiExitShort) {
      return { exit: true, reason: "RSI_OVERSOLD_EXIT" };
    }

    // EMA reversal signals
    if (
      this.position.side === "LONG" &&
      indicators.emaFast < indicators.emaSlow &&
      indicators.prevEmaFast >= indicators.prevEmaSlow
    ) {
      return { exit: true, reason: "EMA_DEATH_CROSS" };
    }
    if (
      this.position.side === "SHORT" &&
      indicators.emaFast > indicators.emaSlow &&
      indicators.prevEmaFast <= indicators.prevEmaSlow
    ) {
      return { exit: true, reason: "EMA_GOLDEN_CROSS" };
    }

    return { exit: false, reason: "" };
  }

  private updateTrailingStops(currentPrice: number): void {
    if (!this.position.side) return;

    if (this.position.side === "LONG") {
      if (
        !this.position.highestPrice ||
        currentPrice > this.position.highestPrice
      ) {
        this.position.highestPrice = currentPrice;
      }
    } else if (this.position.side === "SHORT") {
      if (
        !this.position.lowestPrice ||
        currentPrice < this.position.lowestPrice
      ) {
        this.position.lowestPrice = currentPrice;
      }
    }
  }

  private calculatePositionSize(): number {
    // Calculate position size based on risk management
    const riskAmount = this.balance * this.RISK_PER_TRADE;
    const stopLossDistance = this.STOP_LOSS_PERCENT;

    // Position size = Risk Amount / (Stop Loss Distance * Price)
    const currentPrice = this.candleData[this.candleData.length - 1].close;
    let positionSize = riskAmount / (stopLossDistance * currentPrice);

    // Apply leverage
    positionSize *= this.LEVERAGE;

    // Cap at maximum position size
    positionSize = Math.min(positionSize, this.maxPositionSize);

    return positionSize;
  }

  private async openPosition(
    side: "LONG" | "SHORT",
    price: number,
    reason: string
  ): Promise<void> {
    try {
      const size = this.calculatePositionSize();
      const currentTime = new Date().toISOString();

      if (this.enableRealTrading) {
        // Place real order on Binance
        const orderSide = side === "LONG" ? "BUY" : "SELL";
        const order = await this.binance.order({
          symbol: this.symbol,
          side: orderSide,
          type: "MARKET",
          quantity: size.toFixed(8),
        });

        this.position = {
          side,
          size,
          entryPrice: parseFloat(order.fills[0].price),
          entryTime: currentTime,
          leverage: this.LEVERAGE,
          highestPrice:
            side === "LONG" ? parseFloat(order.fills[0].price) : undefined,
          lowestPrice:
            side === "SHORT" ? parseFloat(order.fills[0].price) : undefined,
          entryReason: reason,
          orderId: order.orderId,
        };

        this.logger.openPosition(
          this.symbol,
          side,
          this.position.entryPrice,
          size,
          reason,
          this.balance,
          this.LEVERAGE,
          this.marketState.regime,
          order.orderId
        );
      } else {
        // Paper trading
        this.position = {
          side,
          size,
          entryPrice: price,
          entryTime: currentTime,
          leverage: this.LEVERAGE,
          highestPrice: side === "LONG" ? price : undefined,
          lowestPrice: side === "SHORT" ? price : undefined,
          entryReason: reason,
        };

        this.logger.openPosition(
          this.symbol,
          side,
          price,
          size,
          reason,
          this.balance,
          this.LEVERAGE,
          this.marketState.regime
        );
      }
    } catch (error) {
      this.logger.error(`Failed to open ${side} position`, error, this.symbol);
    }
  }

  private async closePosition(reason: string): Promise<void> {
    if (!this.position.side) return;

    try {
      const currentTime = new Date().toISOString();
      const currentPrice = this.candleData[this.candleData.length - 1].close;

      if (this.enableRealTrading && this.position.orderId) {
        // Close real position on Binance
        const orderSide = this.position.side === "LONG" ? "SELL" : "BUY";
        const order = await this.binance.order({
          symbol: this.symbol,
          side: orderSide,
          type: "MARKET",
          quantity: this.position.size.toFixed(8),
        });

        const exitPrice = parseFloat(order.fills[0].price);
        const pnlPercent =
          this.position.side === "LONG"
            ? (exitPrice - this.position.entryPrice) / this.position.entryPrice
            : (this.position.entryPrice - exitPrice) / this.position.entryPrice;

        const positionValue = this.position.size / this.LEVERAGE;
        const pnl = positionValue * pnlPercent * this.LEVERAGE;

        // Update balance and daily PnL
        this.balance += pnl;
        this.dailyPnL += pnl;

        // Create trade record
        const trade: Trade = {
          entryTime: this.position.entryTime,
          exitTime: currentTime,
          side: this.position.side,
          entryPrice: this.position.entryPrice,
          exitPrice,
          size: this.position.size,
          pnl,
          pnlPercent: pnlPercent * 100,
          leverage: this.position.leverage,
          entryReason: this.position.entryReason,
          exitReason: reason,
          orderId: order.orderId,
        };

        this.trades.push(trade);

        this.logger.closePosition(
          this.symbol,
          this.position.side,
          exitPrice,
          this.position.size,
          pnl,
          pnlPercent * 100,
          this.balance,
          reason,
          order.orderId
        );
      } else {
        // Paper trading
        const pnlPercent =
          this.position.side === "LONG"
            ? (currentPrice - this.position.entryPrice) /
              this.position.entryPrice
            : (this.position.entryPrice - currentPrice) /
              this.position.entryPrice;

        const positionValue = this.position.size / this.LEVERAGE;
        const pnl = positionValue * pnlPercent * this.LEVERAGE;

        // Cap maximum loss
        const maxLoss = this.balance * 0.04;
        const cappedPnl = Math.max(pnl, -maxLoss);

        this.balance += cappedPnl;
        this.dailyPnL += cappedPnl;

        const trade: Trade = {
          entryTime: this.position.entryTime,
          exitTime: currentTime,
          side: this.position.side,
          entryPrice: this.position.entryPrice,
          exitPrice: currentPrice,
          size: this.position.size,
          pnl: cappedPnl,
          pnlPercent: pnlPercent * 100,
          leverage: this.position.leverage,
          entryReason: this.position.entryReason,
          exitReason: reason,
        };

        this.trades.push(trade);

        this.logger.closePosition(
          this.symbol,
          this.position.side,
          currentPrice,
          this.position.size,
          cappedPnl,
          pnlPercent * 100,
          this.balance,
          reason
        );
      }

      // Reset position
      this.position = {
        side: null,
        size: 0,
        entryPrice: 0,
        entryTime: "",
        leverage: 1,
        entryReason: "",
      };
    } catch (error) {
      this.logger.error(
        `Failed to close ${this.position.side} position`,
        error,
        this.symbol
      );
    }
  }

  private logCurrentStatus(candle: CandleData, indicators: any): void {
    const positionInfo = this.position.side
      ? `${this.position.side} @ ${this.position.entryPrice.toFixed(2)}`
      : "None";

    const pnl = this.position.side ? this.calculateCurrentPnL(candle.close) : 0;

    this.logger.info(
      `💹 ${this.symbol}: ${candle.close.toFixed(
        2
      )} | RSI: ${indicators.rsi.toFixed(1)} | ` +
        `Market: ${this.marketState.regime} | Position: ${positionInfo} | ` +
        `Balance: ${this.balance.toFixed(
          2
        )} USDT | Daily PnL: ${this.dailyPnL.toFixed(2)} USDT`,
      this.symbol
    );
  }

  private calculateCurrentPnL(currentPrice: number): number {
    if (!this.position.side) return 0;

    const pnlPercent =
      this.position.side === "LONG"
        ? (currentPrice - this.position.entryPrice) / this.position.entryPrice
        : (this.position.entryPrice - currentPrice) / this.position.entryPrice;

    const positionValue = this.position.size / this.LEVERAGE;
    return positionValue * pnlPercent * this.LEVERAGE;
  }

  async stop(): Promise<void> {
    this.logger.info("🛑 Stopping trading bot...");
    this.isRunning = false;

    // Close any open position
    if (this.position.side) {
      await this.closePosition("BOT_SHUTDOWN");
    }

    // Stop web server
    await this.webServer.stop();

    this.logger.info("✅ Trading bot stopped successfully");
  }

  // Getters for monitoring
  getBalance(): number {
    return this.balance;
  }

  getPosition(): Position {
    return { ...this.position };
  }

  getTrades(): Trade[] {
    return [...this.trades];
  }

  getMarketState(): MarketState {
    return { ...this.marketState };
  }

  isRunningBot(): boolean {
    return this.isRunning;
  }
}

// Main execution
async function main() {
  const bot = new RealTimeTradingBot();

  // Handle graceful shutdown
  process.on("SIGINT", async () => {
    console.log("\n🛑 Received SIGINT, shutting down gracefully...");
    await bot.stop();
    process.exit(0);
  });

  process.on("SIGTERM", async () => {
    console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
    await bot.stop();
    process.exit(0);
  });

  try {
    await bot.start();

    // Keep the process running
    process.stdin.resume();
  } catch (error) {
    console.error("❌ Failed to start trading bot:", error);
    process.exit(1);
  }
}

// Run the bot
main().catch(console.error);
